/*
 * Qt-based reimplementation of KiCad EDA_ITEM class
 * 
 * This is the root base class for all KiCad objects in Qt version.
 * It provides basic functionality for identification, flags, parent-child
 * relationships, and common operations using Qt frameworks.
 */

#pragma once

// QObject removed - using standard C++ class without Qt object system
#include <QtCore/QString>
#include <QtCore/QStringList>
#include <QtCore/QUuid>
#include <QtCore/QHash>
#include <QtCore/QVector>
#include <QtCore/QFlags>
#include <QtCore/QRectF>
#include <QtCore/QPointF>
#include <QtCore/QDebug>
#include <functional>
#include "qt_temporary_implementations.h"

// Forward declarations for dependencies not in migration scope
class QtUnitsProvider;
class QtEmbeddedFiles;
class QtMsgPanelItem;
class QtEdaSearchData;
class QtShapePolySet;
class QtDrawFrame;
class QtPadStack;
class QtView;
class QtDrillInfo;
class QtEdaItemFlags;
template<typename T> class QtMinOptMax;
class EDA_NETINFO_LIST;
class EDA_BOARD_DATA;
enum class QtPcbLayerId : int;

// Type aliases for compatibility
// QtErrorLoc enum is defined in eda_board_object_data.h
using QtEdaItemFlag = int;

// Qt versions of graphics dependencies (placeholders)
namespace QtKigfx {
    class ViewItem;
}



// Item flags using Qt's QFlags system
enum EDA_OBJECT_DATAFlag : quint32 {
    IsChanged = 0x0001,
    IsNew = 0x0002,
    IsMoving = 0x0004,
    IsResized = 0x0008,
    IsSelected = 0x0010,
    IsEntered = 0x0020,
    IsBrightened = 0x0040,
    IsPasted = 0x0080,
    IsBroken = 0x0100,
    StructDeleted = 0x0200,
    IsLinked = 0x0400,
    InEdit = 0x0800,
    IsRollover = 0x1000,
    Candidate = 0x2000,
    SelectedByDrag = 0x4000,
    SkipStruct = 0x8000,
    DoNotDraw = 0x10000,
    IsShownAsBitmap = 0x20000,
    ForceVisible = 0x40000
};

// Simple typedef for flag collections (replaces Qt's QFlags)
using EDA_OBJECT_DATAFlags = quint32;

// Search and matching enums
enum class QtEdaSearchMatchMode {
    Plain,
    Wildcard,
    WholeWord,
    Permissive
};

enum class QtInspectResult {
    Quit,
    Continue
};

// Forward declaration - actual definition is in qt_temporary_implementations.h
enum class QtBitmaps : int;

/**
 * @brief Function type for inspecting items during iteration
 */
using QtInspectorFunc = std::function<QtInspectResult(class EDA_OBJECT_DATA* item, void* testData)>;
using QtInspector = const QtInspectorFunc&;

// QtEdaSearchData is defined in qt_temporary_implementations.h

/**
 * @brief Standard C++ reimplementation of KiCad's EDA_ITEM class
 * 
 * This is the root base class for all KiCad significant objects.
 * It provides basic functionality for object identification, flags management,
 * parent-child relationships, and common operations. Qt object system has been 
 * removed while preserving Qt containers and geometry classes.
 */
class EDA_OBJECT_DATA
{

public:
    //==========================================================================
    // CONSTRUCTION AND DESTRUCTION
    //==========================================================================
    explicit EDA_OBJECT_DATA(EDA_OBJECT_DATA* parent, QtKicadType itemType);
    explicit EDA_OBJECT_DATA(QtKicadType itemType);
    EDA_OBJECT_DATA(const EDA_OBJECT_DATA& other);
    virtual ~EDA_OBJECT_DATA();

    // Delayed deletion support (replacement for Qt's deleteLater)
    virtual void deleteLater() { delete this; }

    //==========================================================================
    // TYPE IDENTIFICATION AND CLASSIFICATION
    //==========================================================================
    QtKicadType getItemType() const { return m_itemType; }
    QtKicadType getType() const { return m_itemType; } // Alias for compatibility
    
    /**
     * @brief Check if item is one of the specified types
     * @param scanTypes Vector of types to check against
     * @return true if item matches any of the specified types
     */
    virtual bool isType(const QVector<QtKicadType>& scanTypes) const;
    
    /**
     * @brief Static method to check if an item is of specific type
     * @tparam T Target type to check
     * @param item Item to check
     * @return true if item is of type T
     */
    template<typename T>
    static bool isA(const EDA_OBJECT_DATA* item) {
        return item && T::classOf(item);
    }
    
    /**
     * @brief Dynamic cast to specific type with type checking
     * @tparam T Target type for casting
     * @param item Item to cast
     * @return Pointer to T if cast is valid, nullptr otherwise
     */
    template<typename T>
    static T* dynCast(EDA_OBJECT_DATA* item) {
        if (item && T::classOf(item)) {
            return static_cast<T*>(item);
        }
        return nullptr;
    }
    
    /**
     * @brief Get human-readable class name
     */
    virtual QString getClassName() const;
    
    /**
     * @brief Get translated type description
     */
    virtual QString getTypeDescription() const;
    
    virtual QString getFriendlyName() const;
    virtual bool isContainer() const { return false; }
    
    /**
     * @brief Transform shape to polygon for geometric operations
     * @param buffer Output polygon set that will contain the transformed shape
     * @param clearance Additional clearance to add to the shape
     * @param maxError Maximum approximation error for curves
     * @param errorLoc Location of error (inside/outside/center)
     */
    virtual void transformShapeToPolygon(QtShapePolySet& buffer, int clearance = 0, int maxError = 0, int errorLoc = 0) const {
        Q_UNUSED(buffer)
        Q_UNUSED(clearance)
        Q_UNUSED(maxError)
        Q_UNUSED(errorLoc)
    }

    //==========================================================================
    // PARENT-CHILD RELATIONSHIPS
    //==========================================================================
    EDA_OBJECT_DATA* getEdaParent() const { return m_edaParent; }
    virtual void setEdaParent(EDA_OBJECT_DATA* parent) { m_edaParent = parent; }
    virtual void setParent(EDA_OBJECT_DATA* parent) { setEdaParent(parent); } // Alias for compatibility

    virtual void fillChildItemMap(QHash<QUuid, EDA_OBJECT_DATA*>& itemMap) const { Q_UNUSED(itemMap) }

    //==========================================================================
    // FLAGS MANAGEMENT
    //==========================================================================
    EDA_OBJECT_DATAFlags getFlags() const { return m_flags; }
    
    /**
     * @brief Set multiple flags at once
     * @param flags Flags to set
     */
    void setFlags(EDA_OBJECT_DATAFlags flags);
    
    /**
     * @brief Set or clear a single flag
     * @param flag Flag to modify
     * @param enable true to set, false to clear
     */
    void setFlag(EDA_OBJECT_DATAFlag flag, bool enable = true);
    
    /**
     * @brief Clear specified flags (default clears all)
     * @param flags Flags to clear, defaults to all flags
     */
    void clearFlags(EDA_OBJECT_DATAFlags flags = static_cast<EDA_OBJECT_DATAFlags>(0xFFFFFFFF));
    
    bool hasFlag(EDA_OBJECT_DATAFlag flag) const { return (m_flags & flag) != 0; }
    
    // Callback mechanisms to replace Qt signals
    using FlagsChangedCallback = std::function<void(EDA_OBJECT_DATAFlags)>;
    using ModifiedChangedCallback = std::function<void(bool)>;
    using SelectionChangedCallback = std::function<void(bool)>;
    using PositionChangedCallback = std::function<void(const QPointF&)>;
    using ItemChangedCallback = std::function<void()>;
    
    void setFlagsChangedCallback(FlagsChangedCallback callback) { m_flagsChangedCallback = callback; }
    void setModifiedChangedCallback(ModifiedChangedCallback callback) { m_modifiedChangedCallback = callback; }
    void setSelectionChangedCallback(SelectionChangedCallback callback) { m_selectionChangedCallback = callback; }
    void setPositionChangedCallback(PositionChangedCallback callback) { m_positionChangedCallback = callback; }
    void setItemChangedCallback(ItemChangedCallback callback) { m_itemChangedCallback = callback; }
    
    bool isModified() const { return hasFlag(EDA_OBJECT_DATAFlag::IsChanged); }
    bool isNew() const { return hasFlag(EDA_OBJECT_DATAFlag::IsNew); }
    bool isMoving() const { return hasFlag(EDA_OBJECT_DATAFlag::IsMoving); }
    bool isSelected() const { return hasFlag(EDA_OBJECT_DATAFlag::IsSelected); }
    bool isEntered() const { return hasFlag(EDA_OBJECT_DATAFlag::IsEntered); }
    bool isBrightened() const { return hasFlag(EDA_OBJECT_DATAFlag::IsBrightened); }
    bool isRollover() const { return hasFlag(EDA_OBJECT_DATAFlag::IsRollover); }
    bool isForceVisible() const { return hasFlag(EDA_OBJECT_DATAFlag::ForceVisible); }
    bool isShownAsBitmap() const { return hasFlag(EDA_OBJECT_DATAFlag::IsShownAsBitmap); }
    
    void setSelected(bool selected = true) { 
        bool wasSelected = isSelected();
        setFlag(EDA_OBJECT_DATAFlag::IsSelected, selected); 
        if (wasSelected != selected && m_selectionChangedCallback) {
            m_selectionChangedCallback(selected);
        }
    }
    void setBrightened(bool brightened = true) { setFlag(EDA_OBJECT_DATAFlag::IsBrightened, brightened); }
    void setRollover(bool rollover = true) { setFlag(EDA_OBJECT_DATAFlag::IsRollover, rollover); }
    void setForceVisible(bool visible = true) { setFlag(EDA_OBJECT_DATAFlag::ForceVisible, visible); }
    void setShownAsBitmap(bool bitmap = true) { setFlag(EDA_OBJECT_DATAFlag::IsShownAsBitmap, bitmap); }
    
    void clearSelected() { setFlag(EDA_OBJECT_DATAFlag::IsSelected, false); }
    void clearBrightened() { setFlag(EDA_OBJECT_DATAFlag::IsBrightened, false); }
    void setModified();
    
    EDA_OBJECT_DATAFlags getEditFlags() const;
    EDA_OBJECT_DATAFlags getTempFlags() const;
    virtual void clearEditFlags();
    virtual void clearTempFlags();

    //==========================================================================
    // UNIQUE IDENTIFICATION
    //==========================================================================
    const QUuid& getUuid() const { return m_uuid; }
    const QUuid& getId() const { return m_uuid; } // Alias for compatibility

    //==========================================================================
    // GEOMETRIC OPERATIONS
    //==========================================================================
    virtual QRectF getBoundingBox() const;
    virtual QPointF getPosition() const { return QPointF(); }
    virtual void setPosition(const QPointF& position) { Q_UNUSED(position) }
    virtual QPointF getFocusPosition() const { return getPosition(); }
    virtual QPointF getSortPosition() const { return getPosition(); }
    
    // Hit testing
    virtual bool hitTest(const QPointF& position, double accuracy = 0.0) const;
    virtual bool hitTest(const QRectF& rect, bool contained = false, double accuracy = 0.0) const;
    
    // Additional geometric operations needed by derived classes
    virtual QRectF viewBBox() const { return getBoundingBox(); }
    virtual void getMsgPanelInfo(EDA_BOARD_DATA* frame, QVector<QtMsgPanelItem>& items) const { Q_UNUSED(frame); Q_UNUSED(items); }
    virtual QString getItemDescription(QtUnitsProvider* unitsProvider, bool includeType = false) const { Q_UNUSED(unitsProvider); Q_UNUSED(includeType); return getClassName(); }
    virtual void viewGetLayers(QVector<int>& layers) const { Q_UNUSED(layers); }
    virtual double viewGetLOD(int layer, const QtView* view) const { Q_UNUSED(layer); Q_UNUSED(view); return 0.0; }
    virtual bool isOnLayer(int layer) const { Q_UNUSED(layer); return false; }
    virtual bool isOnLayer(QtPcbLayerId layer) const { Q_UNUSED(layer); return false; }
    virtual bool isOnCopperLayer() const { return false; }

    //==========================================================================
    // RENDERING AND VISUALIZATION
    //==========================================================================
    virtual bool renderAsBitmap(double worldScale) const { return false; }
    virtual QtBitmaps getMenuImage() const { return QtBitmaps::DummyItem; }

    //==========================================================================
    // SEARCH AND REPLACE FUNCTIONALITY
    //==========================================================================
    virtual bool matches(const QtEdaSearchData& searchData, void* auxData = nullptr) const;
    virtual bool replace(const QtEdaSearchData& searchData, void* auxData = nullptr);
    virtual bool isReplaceable() const { return false; }
    
    // Static helper for text replacement
    static bool replaceText(const QtEdaSearchData& searchData, QString& text);

    //==========================================================================
    // VISITOR PATTERN FOR ITERATION
    //==========================================================================
    /**
     * @brief Visit this item with an inspector function
     * @param inspector Function to call for inspection
     * @param testData User data passed to inspector
     * @param scanTypes Types to filter during iteration
     * @return Result indicating whether to continue or stop iteration
     */
    virtual QtInspectResult visit(QtInspector inspector, void* testData, 
                                  const QVector<QtKicadType>& scanTypes);
    
    /**
     * @brief Iterate over container items calling inspector for each
     * @tparam Container Container type to iterate
     * @param container Container of items to inspect
     * @param inspector Function to call for each item
     * @param testData User data passed to inspector
     * @param scanTypes Types to filter during iteration
     * @return Result indicating whether iteration was completed or stopped
     */
    template<typename Container>
    static QtInspectResult iterateForward(Container& container, QtInspector inspector,
                                          void* testData, const QVector<QtKicadType>& scanTypes);

    //==========================================================================
    // OBJECT CLONING AND COPYING
    //==========================================================================
    virtual EDA_OBJECT_DATA* clone() const;
    EDA_OBJECT_DATA& operator=(const EDA_OBJECT_DATA& other);
    virtual bool operator<(const EDA_OBJECT_DATA& other) const;
    virtual bool operator==(const EDA_OBJECT_DATA& other) const;
    
    // Helper for STL sorting
    static bool sort(const EDA_OBJECT_DATA* left, const EDA_OBJECT_DATA* right) {
        return *left < *right;
    }

    //==========================================================================
    // USER INTERFACE SUPPORT
    //==========================================================================
    // getItemDescription is already declared above at line 310

    //==========================================================================
    // EMBEDDED FILES SUPPORT
    //==========================================================================
    virtual QtEmbeddedFiles* getEmbeddedFiles() { return nullptr; }

    //==========================================================================
    // QT SERIALIZATION AND DEBUG SUPPORT
    //==========================================================================
    
    // Qt serialization interface
    virtual QVariantMap toVariantMap() const { return QVariantMap(); }
    virtual void fromVariantMap(const QVariantMap& map) { Q_UNUSED(map) }
    
    // Qt debugging interface
    virtual QString toString() const { return getClassName(); }
    
#ifdef QT_DEBUG
    virtual void show(int nestLevel, QDebug& debug) const;
    void showDummy(QDebug& debug) const;
    static QDebug& nestedSpace(int nestLevel, QDebug& debug);
#endif

    // Notification methods to replace Qt signals
    void notifyFlagsChanged(EDA_OBJECT_DATAFlags newFlags) {
        if (m_flagsChangedCallback) m_flagsChangedCallback(newFlags);
    }
    void notifyModifiedChanged(bool isModified) {
        if (m_modifiedChangedCallback) m_modifiedChangedCallback(isModified);
    }
    void notifySelectionChanged(bool isSelected) {
        if (m_selectionChangedCallback) m_selectionChangedCallback(isSelected);
    }
    void notifyPositionChanged(const QPointF& newPosition) {
        if (m_positionChangedCallback) m_positionChangedCallback(newPosition);
    }
    void notifyItemChanged() {
        if (m_itemChangedCallback) m_itemChangedCallback();
    }

protected:
    //==========================================================================
    // PROTECTED HELPER METHODS
    //==========================================================================
    bool matchesText(const QString& text, const QtEdaSearchData& searchData) const;
    void propagateModified(); // Propagate modification to parent

private:
    //==========================================================================
    // PRIVATE DATA MEMBERS
    //==========================================================================
    const QtKicadType m_itemType;      // Runtime type identification (immutable)
    const QUuid m_uuid;                // Unique identifier (immutable)
    EDA_OBJECT_DATAFlags m_flags;            // Item state flags
    EDA_OBJECT_DATA* m_edaParent;           // Parent item
    
    // Callback storage (replaces Qt signals)
    FlagsChangedCallback m_flagsChangedCallback;
    ModifiedChangedCallback m_modifiedChangedCallback;
    SelectionChangedCallback m_selectionChangedCallback;
    PositionChangedCallback m_positionChangedCallback;
    ItemChangedCallback m_itemChangedCallback;
    
    // Static type registration system
    static QHash<QtKicadType, QString> s_typeNames;
    static QHash<QtKicadType, QString> s_typeDescriptions;
    
    static void initializeTypeSystem();
    static QString getTypeName(QtKicadType type);
};

/**
 * @brief Comparison functor for sorting EDA_OBJECT_DATA pointers by UUID
 */
struct QtCompareByUuid {
    bool operator()(const EDA_OBJECT_DATA* item1, const EDA_OBJECT_DATA* item2) const {
        Q_ASSERT(item1 && item2);
        
        if (item1->getUuid() == item2->getUuid()) {
            return item1 < item2; // Use pointer comparison for stable sort
        }
        
        return item1->getUuid().toString() < item2->getUuid().toString();
    }
};

/**
 * @brief Qt container type aliases
 */
using EDA_OBJECT_DATAs = QVector<EDA_OBJECT_DATA*>;
using EDA_OBJECT_DATASet = QSet<EDA_OBJECT_DATA*>;

/**
 * @brief Helper function for cloning items in containers
 */
inline EDA_OBJECT_DATA* newClone(const EDA_OBJECT_DATA& item) { 
    return item.clone(); 
}

//=============================================================================
// TEMPLATE IMPLEMENTATIONS
//=============================================================================

template<typename Container>
QtInspectResult EDA_OBJECT_DATA::iterateForward(Container& container, QtInspector inspector,
                                          void* testData, const QVector<QtKicadType>& scanTypes)
{
    for (auto* item : container) {
        if (item) {
            EDA_OBJECT_DATA* edaItem = dynamic_cast<EDA_OBJECT_DATA*>(item);
            if (edaItem && edaItem->visit(inspector, testData, scanTypes) == QtInspectResult::Quit) {
                return QtInspectResult::Quit;
            }
        }
    }
    return QtInspectResult::Continue;
}

//=============================================================================
// TYPE SUPPORT FOR STL CONTAINERS AND STANDARD C++
//=============================================================================
// Note: Qt metatype declarations removed - using standard C++ type system instead
// These types can now be used with standard STL containers and algorithms