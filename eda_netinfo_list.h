/*
 * This program source code file is part of KiCad, a free EDA CAD application.
 *
 * Copyright The KiCad Developers, see AUTHORS.txt for contributors.
 *
 * This program is free software; you can redistribute it and/or
 * modify it under the terms of the GNU General Public License
 * as published by the Free Software Foundation; either version 2
 * of the License, or (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with this program; if not, you may find one here:
 * http://www.gnu.org/licenses/old-licenses/gpl-2.0.html
 * or you may search the http://www.gnu.org website for the version 2 license,
 * or you may write to the Free Software Foundation, Inc.,
 * 51 Franklin Street, Fifth Floor, Boston, MA  02110-1301, USA
 */

#ifndef QT_NETINFO_LIST_H
#define QT_NETINFO_LIST_H

#include <QString>
#include <QMap>
#include <QHash>
#include <QVector>
#include <QSharedPointer>
#include <QMutex>
#include <functional>
#include "eda_net_data.h"

// 前向声明
class EDA_BOARD_DATA;
class QtBoardCommit;

// Qt版本的网络映射类型定义
using QtNetnamesMap = QMap<QString, EDA_NET_DATA*>;
using QtNetcodesMap = QMap<int, EDA_NET_DATA*>;

/**
 * 网络代码映射类，用于导出时将网络代码映射为连续数字
 * Standard C++ class using Qt containers but not Qt object system
 */
class KiCadNetinfoMapping
{
public:
    explicit KiCadNetinfoMapping();
    ~KiCadNetinfoMapping() = default;

    // 设置板对象
    void setBoard(const EDA_BOARD_DATA* board);
    const EDA_BOARD_DATA* getBoard() const { return m_board; }

    // 更新映射
    void update();

    // 转换网络代码
    int translate(int netCode) const;

    // 获取映射大小
    int getSize() const { return m_netMapping.size(); }

    // 迭代器类
    class Iterator
    {
    public:
        Iterator(QMap<int, int>::const_iterator iter, const KiCadNetinfoMapping* mapping);

        // 迭代器操作
        const Iterator& operator++();
        Iterator operator++(int);
        EDA_NET_DATA* operator*() const;
        EDA_NET_DATA* operator->() const;
        bool operator!=(const Iterator& other) const;
        bool operator==(const Iterator& other) const;

    private:
        QMap<int, int>::const_iterator m_iterator;  
        const KiCadNetinfoMapping* m_mapping;
    };

    // 迭代器接口
    Iterator begin() const;
    Iterator end() const;

    // Callback mechanisms to replace Qt signals
    using MappingUpdatedCallback = std::function<void()>;
    
    void setMappingUpdatedCallback(MappingUpdatedCallback callback) { m_mappingUpdatedCallback = callback; }
    void notifyMappingUpdated() { if (m_mappingUpdatedCallback) m_mappingUpdatedCallback(); }

private:
    const EDA_BOARD_DATA* m_board;
    QMap<int, int> m_netMapping;  // 网络代码映射表
    MappingUpdatedCallback m_mappingUpdatedCallback;
};

/**
 * 网络信息列表容器，管理EDA_NET_DATA集合
 * Standard C++ class using Qt containers but not Qt object system
 */
class EDA_NETINFO_LIST
{

public:
    explicit EDA_NETINFO_LIST(EDA_BOARD_DATA* parent = nullptr);
    ~EDA_NETINFO_LIST();

    // 常量定义
    static const int UNCONNECTED = 0;  // 未连接网络代码
    static const int ORPHANED = -1;    // 孤儿网络代码

    // 静态孤儿项
    static EDA_NET_DATA* orphanedItem();

    // 查找功能
    EDA_NET_DATA* getNetItem(int netCode) const;
    EDA_NET_DATA* getNetItem(const QString& netName) const;
    EDA_NET_DATA* findNet(const QString& netName) const { return getNetItem(netName); }
    EDA_NET_DATA* findNet(int netCode) const { return getNetItem(netCode); }

    // 网络数量
    int getNetCount() const { return m_netNames.size(); }

    // 获取映射（主要用于Python绑定）
    const QtNetnamesMap& netsByName() const { return m_netNames; }
    const QtNetcodesMap& netsByNetcode() const { return m_netCodes; }

    // 显示网络名称管理
    bool isDisplayNetnamesDirty() const { return m_displayNetnamesDirty; }
    void setDisplayNetnamesDirty(bool dirty);
    void rebuildDisplayNetnames() const;

    // 父对象管理
    EDA_BOARD_DATA* getParent() const { return m_parent; }

    // 迭代器支持
    class Iterator
    {
    public:
        Iterator(QtNetnamesMap::const_iterator iter);

        // 迭代器操作
        const Iterator& operator++();
        Iterator operator++(int);
        EDA_NET_DATA* operator*() const;
        EDA_NET_DATA* operator->() const;
        bool operator!=(const Iterator& other) const;
        bool operator==(const Iterator& other) const;

    private:
        QtNetnamesMap::const_iterator m_iterator;
    };

    Iterator begin() const;
    Iterator end() const;

    // 调试功能
    void show() const;

    // 网络管理
    void appendNet(EDA_NET_DATA* newElement);
    void removeNet(EDA_NET_DATA* net);
    void removeUnusedNets(QtBoardCommit* commit = nullptr);
    void clear();

    // 构建网络列表
    void buildListOfNets();

    // Callback mechanisms to replace Qt signals
    using NetCountChangedCallback = std::function<void(int)>;
    using NetAddedCallback = std::function<void(EDA_NET_DATA*)>;
    using NetRemovedCallback = std::function<void(EDA_NET_DATA*)>;
    using NetChangedCallback = std::function<void(EDA_NET_DATA*)>;
    using DisplayNetnamesDirtyChangedCallback = std::function<void(bool)>;
    using NetsClearedCallback = std::function<void()>;
    using NetsRebuiltCallback = std::function<void()>;
    
    void setNetCountChangedCallback(NetCountChangedCallback callback) { m_netCountChangedCallback = callback; }
    void setNetAddedCallback(NetAddedCallback callback) { m_netAddedCallback = callback; }
    void setNetRemovedCallback(NetRemovedCallback callback) { m_netRemovedCallback = callback; }
    void setNetChangedCallback(NetChangedCallback callback) { m_netChangedCallback = callback; }
    void setDisplayNetnamesDirtyChangedCallback(DisplayNetnamesDirtyChangedCallback callback) { m_displayNetnamesDirtyChangedCallback = callback; }
    void setNetsClearedCallback(NetsClearedCallback callback) { m_netsClearedCallback = callback; }
    void setNetsRebuiltCallback(NetsRebuiltCallback callback) { m_netsRebuiltCallback = callback; }

    // 响应网络项变化
    void onNetItemChanged();
    void onNetItemDestroyed();

private:
    // 核心数据
    EDA_BOARD_DATA* m_parent;                      // 父板对象
    QtNetnamesMap m_netNames;               // 按名称索引的网络映射
    QtNetcodesMap m_netCodes;               // 按代码索引的网络映射
    int m_newNetCode;                       // 新网络代码分配计数器

    // 状态标志
    mutable bool m_displayNetnamesDirty;    // 显示网络名称脏标志
    mutable QMutex m_mutex;                 // 线程安全保护
    
    // Callback storage (replaces Qt signals)
    NetCountChangedCallback m_netCountChangedCallback;
    NetAddedCallback m_netAddedCallback;
    NetRemovedCallback m_netRemovedCallback;
    NetChangedCallback m_netChangedCallback;
    DisplayNetnamesDirtyChangedCallback m_displayNetnamesDirtyChangedCallback;
    NetsClearedCallback m_netsClearedCallback;
    NetsRebuiltCallback m_netsRebuiltCallback;

    // 辅助方法
    int getFreeNetCode();
    QString unescapeString(const QString& str) const;

    // 验证方法
    bool validateNetItem(EDA_NET_DATA* netItem) const;
    void ensureUnconnectedNet();
    
    // Disable copy constructor and assignment operator
    EDA_NETINFO_LIST(const EDA_NETINFO_LIST&) = delete;
    EDA_NETINFO_LIST& operator=(const EDA_NETINFO_LIST&) = delete;
};

#endif /* QT_NETINFO_LIST_H */